# Hướng dẫn Unit Testing cho Attributes

## Tổng quan

Unit tests kiểm tra các function và logic riêng lẻ trong hệ thống attributes. Chúng ta sử dụng Jest làm framework testing chính.

## Cài đặt và Cấu hình

### Cài đặt Dependencies

```bash
npm install --save-dev jest @types/jest ts-jest
npm install --save-dev @testing-library/jest-dom
```

### C<PERSON>u hình Jest

File `jest.config.js`:

```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/tests/unit'],
  testMatch: ['**/*.test.ts'],
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts'
  ],
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'lcov', 'html']
};
```

## Cấu trúc Unit Tests

### 1. Utility Functions Tests

**File**: `tests/unit/lib/utils/attributes.test.ts`

```typescript
import {
  generateSlug,
  needsValues,
  validateAttribute,
  getColorCode
} from "@/lib/utils/attributes";

describe("Attribute Utilities", () => {
  describe("generateSlug", () => {
    it("should generate slug from Vietnamese text", () => {
      expect(generateSlug("Màu sắc")).toBe("mau-sac");
      expect(generateSlug("Kích thước")).toBe("kich-thuoc");
    });

    it("should handle special characters", () => {
      expect(generateSlug("Áo dài truyền thống")).toBe("ao-dai-truyen-thong");
    });
  });

  describe("needsValues", () => {
    it("should return true for types that need values", () => {
      expect(needsValues("COLOR")).toBe(true);
      expect(needsValues("SIZE")).toBe(true);
    });

    it("should return false for types that don't need values", () => {
      expect(needsValues("TEXT")).toBe(false);
      expect(needsValues("BOOLEAN")).toBe(false);
    });
  });
});
```

### 2. API Routes Tests

**File**: `tests/unit/api/admin/attributes.test.ts`

```typescript
import { GET, POST } from "@/app/api/admin/attributes/route";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";

// Mock dependencies
jest.mock("@/lib/prisma");
jest.mock("next-auth");

describe("/api/admin/attributes", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("GET", () => {
    it("should return 401 when not authenticated", async () => {
      (getServerSession as jest.Mock).mockResolvedValue(null);
      
      const request = new NextRequest("http://localhost/api/admin/attributes");
      const response = await GET(request);
      
      expect(response.status).toBe(401);
    });

    it("should return attributes with pagination", async () => {
      (getServerSession as jest.Mock).mockResolvedValue({
        user: { role: "ADMIN" }
      });

      const mockAttributes = [
        {
          id: "1",
          name: "Màu sắc",
          type: "COLOR"
        }
      ];

      (prisma.attribute.findMany as jest.Mock).mockResolvedValue(mockAttributes);
      (prisma.attribute.count as jest.Mock).mockResolvedValue(1);

      const request = new NextRequest("http://localhost/api/admin/attributes");
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.data).toEqual(mockAttributes);
    });
  });
});
```

### 3. Model Tests

**File**: `tests/unit/models/attribute.test.ts`

```typescript
import { prisma } from "@/lib/prisma";

describe("Attribute Model", () => {
  beforeEach(async () => {
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
  });

  it("should create an attribute with required fields", async () => {
    const attributeData = {
      name: "Màu sắc",
      slug: "mau-sac",
      type: "COLOR",
      isRequired: false,
      isFilterable: true,
      sortOrder: 1,
    };

    const attribute = await prisma.attribute.create({
      data: attributeData,
    });

    expect(attribute.name).toBe(attributeData.name);
    expect(attribute.slug).toBe(attributeData.slug);
    expect(attribute.type).toBe(attributeData.type);
  });

  it("should enforce unique slug constraint", async () => {
    const attributeData = {
      name: "Test Attribute",
      slug: "test-attribute",
      type: "TEXT",
    };

    await prisma.attribute.create({ data: attributeData });

    await expect(
      prisma.attribute.create({
        data: { ...attributeData, name: "Another Test" }
      })
    ).rejects.toThrow();
  });
});
```

## Chạy Unit Tests

### Chạy tất cả Unit Tests

```bash
# Chạy tất cả unit tests
npm run test:unit

# Chạy với watch mode
npm run test:unit:watch

# Chạy với coverage
npm run test:unit:coverage
```

### Chạy Tests cụ thể

```bash
# Chạy tests cho utilities
npm test tests/unit/lib/utils/attributes.test.ts

# Chạy tests cho API routes
npm test tests/unit/api/admin/attributes.test.ts

# Chạy tests cho models
npm test tests/unit/models/attribute.test.ts
```

### Chạy Tests với Pattern

```bash
# Chạy tests có tên chứa "attribute"
npm test -- --testNamePattern="attribute"

# Chạy tests trong thư mục cụ thể
npm test tests/unit/lib/

# Chạy một test cụ thể
npm test -- --testNamePattern="should generate slug"
```

## Test Scenarios

### 1. Slug Generation Tests

```typescript
describe("generateSlug", () => {
  const testCases = [
    { input: "Màu sắc", expected: "mau-sac" },
    { input: "Kích thước", expected: "kich-thuoc" },
    { input: "Chất liệu", expected: "chat-lieu" },
    { input: "Áo dài truyền thống", expected: "ao-dai-truyen-thong" },
    { input: "Đồ bơi nữ", expected: "do-boi-nu" },
    { input: "  Màu   sắc   đặc biệt  ", expected: "mau-sac-dac-biet" },
    { input: "", expected: "" },
    { input: "   ", expected: "" }
  ];

  testCases.forEach(({ input, expected }) => {
    it(`should convert "${input}" to "${expected}"`, () => {
      expect(generateSlug(input)).toBe(expected);
    });
  });
});
```

### 2. Validation Tests

```typescript
describe("validateAttribute", () => {
  it("should validate required fields", () => {
    const invalidData = { name: "", type: "TEXT" };
    const result = validateAttribute(invalidData);
    
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Tên thuộc tính là bắt buộc");
  });

  it("should require values for certain types", () => {
    const invalidData = {
      name: "Màu sắc",
      type: "COLOR",
      values: []
    };
    
    const result = validateAttribute(invalidData);
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Loại thuộc tính Màu sắc cần ít nhất một giá trị");
  });

  it("should detect duplicate values", () => {
    const invalidData = {
      name: "Màu sắc",
      type: "COLOR",
      values: [
        { value: "Đỏ", slug: "do" },
        { value: "đỏ", slug: "do-2" }
      ]
    };
    
    const result = validateAttribute(invalidData);
    expect(result.isValid).toBe(false);
    expect(result.errors).toContain("Không được có giá trị trùng lặp");
  });
});
```

### 3. Color Code Tests

```typescript
describe("getColorCode", () => {
  const colorTests = [
    { color: "đỏ", expected: "#ef4444" },
    { color: "xanh dương", expected: "#3b82f6" },
    { color: "xanh lá", expected: "#22c55e" },
    { color: "vàng", expected: "#eab308" },
    { color: "đen", expected: "#000000" },
    { color: "trắng", expected: "#ffffff" },
    { color: "unknown", expected: "#6b7280" }
  ];

  colorTests.forEach(({ color, expected }) => {
    it(`should return ${expected} for ${color}`, () => {
      expect(getColorCode(color)).toBe(expected);
    });
  });

  it("should be case insensitive", () => {
    expect(getColorCode("ĐỎ")).toBe("#ef4444");
    expect(getColorCode("RED")).toBe("#ef4444");
  });
});
```

## Mocking Strategies

### 1. Database Mocking

```typescript
// Mock Prisma
jest.mock("@/lib/prisma", () => ({
  prisma: {
    attribute: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      count: jest.fn(),
    },
    attributeValue: {
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    }
  }
}));
```

### 2. Authentication Mocking

```typescript
// Mock NextAuth
jest.mock("next-auth", () => ({
  getServerSession: jest.fn(),
}));

// In test
(getServerSession as jest.Mock).mockResolvedValue({
  user: { role: "ADMIN" }
});
```

### 3. External Service Mocking

```typescript
// Mock external API calls
jest.mock("@/lib/external-api", () => ({
  uploadImage: jest.fn().mockResolvedValue({ url: "mock-url" }),
  validateSlug: jest.fn().mockResolvedValue(true),
}));
```

## Coverage và Reporting

### Coverage Targets

- **Functions**: 90%+
- **Lines**: 85%+
- **Branches**: 80%+
- **Statements**: 85%+

### Xem Coverage Report

```bash
# Generate coverage report
npm run test:unit:coverage

# Open HTML report
open coverage/lcov-report/index.html
```

### Coverage Configuration

```javascript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    'src/lib/utils/attributes.ts',
    'src/app/api/admin/attributes/**/*.ts',
    'src/hooks/useAttributes.ts',
    'src/hooks/useAttributeValues.ts',
    '!src/**/*.d.ts',
    '!src/**/*.test.ts'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 90,
      lines: 85,
      statements: 85
    }
  }
};
```

## Best Practices

### 1. Test Organization

```typescript
describe("Component/Function Name", () => {
  describe("specific functionality", () => {
    beforeEach(() => {
      // Setup for this group
    });

    it("should do something specific", () => {
      // Test implementation
    });
  });
});
```

### 2. Test Data

```typescript
// Use factories for test data
const createTestAttribute = (overrides = {}) => ({
  name: "Test Attribute",
  slug: "test-attribute",
  type: "TEXT",
  isRequired: false,
  isFilterable: true,
  sortOrder: 0,
  ...overrides
});
```

### 3. Assertions

```typescript
// Be specific with assertions
expect(result).toEqual({
  isValid: false,
  errors: ["Tên thuộc tính là bắt buộc"]
});

// Use appropriate matchers
expect(array).toHaveLength(3);
expect(string).toContain("expected text");
expect(number).toBeGreaterThan(0);
```

### 4. Error Testing

```typescript
it("should handle database errors", async () => {
  (prisma.attribute.create as jest.Mock).mockRejectedValue(
    new Error("Database connection failed")
  );

  await expect(createAttribute(validData)).rejects.toThrow(
    "Database connection failed"
  );
});
```

## Troubleshooting

### Common Issues

1. **Mock not working**: Ensure mock is called before import
2. **Async issues**: Use proper await/async
3. **Database cleanup**: Reset mocks between tests
4. **Type errors**: Use proper TypeScript types

### Debug Tips

```typescript
// Add debug logging
console.log("Test data:", testData);

// Use test.only for focused testing
it.only("should debug this specific test", () => {
  // Test implementation
});

// Skip problematic tests temporarily
it.skip("should fix this later", () => {
  // Test implementation
});
```
