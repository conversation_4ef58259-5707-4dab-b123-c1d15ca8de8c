# Hướng dẫn End-to-End Testing cho Attributes

## Tổng quan

End-to-End (E2E) tests kiểm tra toàn bộ user flow từ giao diện người dùng đến database, đả<PERSON> bảo hệ thống hoạt động đúng từ góc nhìn của người dùng cuối.

## Cài đặt và Cấu hình

### <PERSON>ài đặt Playwright

```bash
# Cài đặt Playwright
npm install --save-dev @playwright/test

# Cài đặt browsers
npx playwright install

# Cài đặt system dependencies (Linux)
npx playwright install-deps
```

### <PERSON><PERSON><PERSON> hì<PERSON> Playwright

**File**: `playwright.config.ts`

```typescript
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
});
```

## Cấu trúc E2E Tests

### 1. Attributes List Page Tests

**File**: `tests/e2e/admin/attributes/attributes-list.spec.ts`

```typescript
import { test, expect } from "@playwright/test";
import { prisma } from "@/lib/prisma";

test.describe("Attributes List Page", () => {
  test.beforeEach(async ({ page }) => {
    // Setup test data
    await prisma.attribute.createMany({
      data: [
        {
          name: "Màu sắc",
          slug: "mau-sac",
          type: "COLOR",
          isRequired: false,
          isFilterable: true,
          sortOrder: 1,
        },
        {
          name: "Kích thước",
          slug: "kich-thuoc",
          type: "SIZE",
          isRequired: true,
          isFilterable: true,
          sortOrder: 2,
        }
      ]
    });

    await page.goto("/admin/attributes");
  });

  test("should display attributes list", async ({ page }) => {
    // Check page title
    await expect(page.locator("h1")).toContainText("Quản lý thuộc tính");

    // Check attributes are displayed
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(2);
    await expect(page.locator("text=Màu sắc")).toBeVisible();
    await expect(page.locator("text=Kích thước")).toBeVisible();

    // Check badges
    await expect(page.locator('[data-testid="required-badge"]')).toHaveCount(1);
    await expect(page.locator('[data-testid="filterable-badge"]')).toHaveCount(2);
  });

  test("should search attributes", async ({ page }) => {
    // Search for "màu"
    await page.fill('[data-testid="search-input"]', "màu");
    await page.press('[data-testid="search-input"]', "Enter");

    // Should show only color attribute
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(1);
    await expect(page.locator("text=Màu sắc")).toBeVisible();
    await expect(page.locator("text=Kích thước")).not.toBeVisible();
  });

  test("should filter by type", async ({ page }) => {
    await page.click('[data-testid="type-filter"]');
    await page.click('text=Màu sắc');

    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(1);
    await expect(page.locator("text=Màu sắc")).toBeVisible();
  });

  test("should delete attribute", async ({ page }) => {
    await page.click('[data-testid="attribute-row"]:first-child [data-testid="delete-btn"]');
    
    // Confirm deletion
    await expect(page.locator('[data-testid="delete-dialog"]')).toBeVisible();
    await page.click('[data-testid="confirm-delete-btn"]');

    // Verify deletion
    await expect(page.locator("text=Xóa thuộc tính thành công")).toBeVisible();
    await expect(page.locator('[data-testid="attribute-row"]')).toHaveCount(1);
  });
});
```

### 2. Create Attribute Tests

**File**: `tests/e2e/admin/attributes/create-attribute.spec.ts`

```typescript
test.describe("Create Attribute Page", () => {
  test.beforeEach(async ({ page }) => {
    await page.goto("/admin/attributes/create");
  });

  test("should create text attribute", async ({ page }) => {
    // Fill form
    await page.fill('[data-testid="name-input"]', "Mô tả sản phẩm");
    await page.fill('[data-testid="description-input"]', "Mô tả chi tiết");
    
    // Select type
    await page.click('[data-testid="type-select"]');
    await page.click('text=Văn bản');

    // Submit
    await page.click('[data-testid="submit-btn"]');

    // Verify success
    await expect(page.locator("text=Tạo thuộc tính thành công")).toBeVisible();
    await expect(page).toHaveURL("/admin/attributes");
  });

  test("should create color attribute with values", async ({ page }) => {
    // Fill basic info
    await page.fill('[data-testid="name-input"]', "Màu sắc");
    await page.click('[data-testid="type-select"]');
    await page.click('text=Màu sắc');

    // Add values
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-0"]', "Đỏ");

    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="value-input-1"]', "Xanh");

    // Submit
    await page.click('[data-testid="submit-btn"]');

    // Verify
    await expect(page.locator("text=Tạo thuộc tính thành công")).toBeVisible();
    
    // Check in database
    const attribute = await prisma.attribute.findFirst({
      where: { name: "Màu sắc" },
      include: { values: true }
    });
    
    expect(attribute).toBeTruthy();
    expect(attribute?.values).toHaveLength(2);
  });

  test("should validate required fields", async ({ page }) => {
    // Try to submit without name
    await page.click('[data-testid="submit-btn"]');
    
    await expect(page.locator("text=Tên thuộc tính là bắt buộc")).toBeVisible();
  });

  test("should auto-generate slug", async ({ page }) => {
    await page.fill('[data-testid="name-input"]', "Màu sắc đặc biệt");
    
    await expect(page.locator('[data-testid="slug-input"]')).toHaveValue("mau-sac-dac-biet");
  });
});
```

### 3. Edit Attribute Tests

**File**: `tests/e2e/admin/attributes/edit-attribute.spec.ts`

```typescript
test.describe("Edit Attribute Page", () => {
  let testAttributeId: string;

  test.beforeEach(async ({ page }) => {
    // Create test attribute
    const testAttribute = await prisma.attribute.create({
      data: {
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR",
        description: "Màu sắc sản phẩm",
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
      }
    });

    testAttributeId = testAttribute.id;

    // Create values
    await prisma.attributeValue.createMany({
      data: [
        { attributeId: testAttributeId, value: "Đỏ", slug: "do", sortOrder: 1 },
        { attributeId: testAttributeId, value: "Xanh", slug: "xanh", sortOrder: 2 }
      ]
    });

    await page.goto(`/admin/attributes/${testAttributeId}/edit`);
  });

  test("should display pre-filled form", async ({ page }) => {
    await expect(page.locator('[data-testid="name-input"]')).toHaveValue("Màu sắc");
    await expect(page.locator('[data-testid="slug-input"]')).toHaveValue("mau-sac");
    await expect(page.locator('[data-testid="description-input"]')).toHaveValue("Màu sắc sản phẩm");
    
    // Check values
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(2);
    await expect(page.locator("text=Đỏ")).toBeVisible();
    await expect(page.locator("text=Xanh")).toBeVisible();
  });

  test("should update attribute information", async ({ page }) => {
    // Update name
    await page.fill('[data-testid="name-input"]', "Màu sắc cập nhật");
    await page.fill('[data-testid="description-input"]', "Mô tả cập nhật");
    
    // Toggle required
    await page.check('[data-testid="required-switch"]');

    // Submit
    await page.click('[data-testid="submit-btn"]');

    // Verify
    await expect(page.locator("text=Cập nhật thuộc tính thành công")).toBeVisible();

    // Check database
    const updatedAttribute = await prisma.attribute.findUnique({
      where: { id: testAttributeId }
    });

    expect(updatedAttribute?.name).toBe("Màu sắc cập nhật");
    expect(updatedAttribute?.isRequired).toBe(true);
  });

  test("should add new value", async ({ page }) => {
    await page.click('[data-testid="add-value-btn"]');
    await page.fill('[data-testid="new-value-input"]', "Vàng");
    await page.click('[data-testid="save-value-btn"]');

    await expect(page.locator("text=Tạo giá trị thành công")).toBeVisible();
    await expect(page.locator("text=Vàng")).toBeVisible();
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(3);
  });

  test("should delete value", async ({ page }) => {
    await page.click('[data-testid="value-row"]:first-child [data-testid="delete-value-btn"]');
    await page.click('[data-testid="confirm-delete-value-btn"]');

    await expect(page.locator("text=Xóa giá trị thành công")).toBeVisible();
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(1);
    await expect(page.locator("text=Đỏ")).not.toBeVisible();
  });

  test("should reorder values with drag and drop", async ({ page }) => {
    // Drag second value to first position
    const secondValue = page.locator('[data-testid="value-row"]:nth-child(2)');
    const firstValue = page.locator('[data-testid="value-row"]:nth-child(1)');

    await secondValue.dragTo(firstValue);

    await expect(page.locator("text=Cập nhật thứ tự thành công")).toBeVisible();

    // Check new order
    const valueRows = page.locator('[data-testid="value-row"]');
    await expect(valueRows.nth(0)).toContainText("Xanh");
    await expect(valueRows.nth(1)).toContainText("Đỏ");
  });
});
```

### 4. Attribute Detail Tests

**File**: `tests/e2e/admin/attributes/attribute-detail.spec.ts`

```typescript
test.describe("Attribute Detail Page", () => {
  let testAttributeId: string;

  test.beforeEach(async ({ page }) => {
    const testAttribute = await prisma.attribute.create({
      data: {
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR",
        description: "Màu sắc sản phẩm",
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
      }
    });

    testAttributeId = testAttribute.id;

    await prisma.attributeValue.createMany({
      data: [
        { attributeId: testAttributeId, value: "Đỏ", slug: "do", sortOrder: 1 },
        { attributeId: testAttributeId, value: "Xanh", slug: "xanh", sortOrder: 2 }
      ]
    });

    await page.goto(`/admin/attributes/${testAttributeId}`);
  });

  test("should display attribute details", async ({ page }) => {
    await expect(page.locator("h1")).toContainText("Màu sắc");
    await expect(page.locator('[data-testid="attribute-name"]')).toContainText("Màu sắc");
    await expect(page.locator('[data-testid="attribute-slug"]')).toContainText("mau-sac");
    await expect(page.locator('[data-testid="attribute-type"]')).toContainText("Màu sắc");
    await expect(page.locator('[data-testid="attribute-description"]')).toContainText("Màu sắc sản phẩm");
  });

  test("should display statistics", async ({ page }) => {
    await expect(page.locator('[data-testid="values-count"]')).toContainText("2");
    await expect(page.locator('[data-testid="products-count"]')).toContainText("0");
  });

  test("should display values list", async ({ page }) => {
    await expect(page.locator('[data-testid="values-section"]')).toBeVisible();
    await expect(page.locator('[data-testid="value-row"]')).toHaveCount(2);
    
    const firstRow = page.locator('[data-testid="value-row"]').first();
    await expect(firstRow.locator('[data-testid="value-name"]')).toContainText("Đỏ");
    await expect(firstRow.locator('[data-testid="value-slug"]')).toContainText("do");
  });

  test("should navigate to edit page", async ({ page }) => {
    await page.click('[data-testid="edit-btn"]');
    await expect(page).toHaveURL(`/admin/attributes/${testAttributeId}/edit`);
  });

  test("should delete attribute", async ({ page }) => {
    await page.click('[data-testid="delete-btn"]');
    await page.click('[data-testid="confirm-delete-btn"]');

    await expect(page.locator("text=Xóa thuộc tính thành công")).toBeVisible();
    await expect(page).toHaveURL("/admin/attributes");

    // Verify deletion
    const attribute = await prisma.attribute.findUnique({
      where: { id: testAttributeId }
    });
    expect(attribute).toBeNull();
  });
});
```

## Chạy E2E Tests

### Commands

```bash
# Chạy tất cả E2E tests
npx playwright test

# Chạy tests cụ thể
npx playwright test tests/e2e/admin/attributes/

# Chạy với UI mode
npx playwright test --ui

# Chạy với headed browser
npx playwright test --headed

# Chạy trên browser cụ thể
npx playwright test --project=chromium

# Debug mode
npx playwright test --debug

# Chạy với trace
npx playwright test --trace on
```

### Scripts trong package.json

```json
{
  "scripts": {
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:e2e:headed": "playwright test --headed",
    "test:e2e:debug": "playwright test --debug",
    "test:e2e:report": "playwright show-report"
  }
}
```

## Page Object Model

### Base Page

```typescript
// tests/e2e/pages/BasePage.ts
export class BasePage {
  constructor(protected page: Page) {}

  async goto(url: string) {
    await this.page.goto(url);
  }

  async waitForLoadState() {
    await this.page.waitForLoadState('networkidle');
  }

  async takeScreenshot(name: string) {
    await this.page.screenshot({ path: `screenshots/${name}.png` });
  }
}
```

### Attributes List Page

```typescript
// tests/e2e/pages/AttributesListPage.ts
export class AttributesListPage extends BasePage {
  private readonly searchInput = '[data-testid="search-input"]';
  private readonly createButton = '[data-testid="create-attribute-btn"]';
  private readonly attributeRows = '[data-testid="attribute-row"]';
  private readonly typeFilter = '[data-testid="type-filter"]';

  async search(term: string) {
    await this.page.fill(this.searchInput, term);
    await this.page.press(this.searchInput, 'Enter');
  }

  async clickCreate() {
    await this.page.click(this.createButton);
  }

  async getAttributeCount() {
    return await this.page.locator(this.attributeRows).count();
  }

  async filterByType(type: string) {
    await this.page.click(this.typeFilter);
    await this.page.click(`text=${type}`);
  }

  async deleteAttribute(index: number) {
    await this.page.click(`${this.attributeRows}:nth-child(${index + 1}) [data-testid="delete-btn"]`);
    await this.page.click('[data-testid="confirm-delete-btn"]');
  }
}
```

### Create Attribute Page

```typescript
// tests/e2e/pages/CreateAttributePage.ts
export class CreateAttributePage extends BasePage {
  private readonly nameInput = '[data-testid="name-input"]';
  private readonly typeSelect = '[data-testid="type-select"]';
  private readonly submitButton = '[data-testid="submit-btn"]';
  private readonly addValueButton = '[data-testid="add-value-btn"]';

  async fillName(name: string) {
    await this.page.fill(this.nameInput, name);
  }

  async selectType(type: string) {
    await this.page.click(this.typeSelect);
    await this.page.click(`text=${type}`);
  }

  async addValue(value: string, index: number) {
    await this.page.click(this.addValueButton);
    await this.page.fill(`[data-testid="value-input-${index}"]`, value);
  }

  async submit() {
    await this.page.click(this.submitButton);
  }

  async createTextAttribute(name: string, description?: string) {
    await this.fillName(name);
    if (description) {
      await this.page.fill('[data-testid="description-input"]', description);
    }
    await this.selectType('Văn bản');
    await this.submit();
  }

  async createColorAttribute(name: string, values: string[]) {
    await this.fillName(name);
    await this.selectType('Màu sắc');
    
    for (let i = 0; i < values.length; i++) {
      await this.addValue(values[i], i);
    }
    
    await this.submit();
  }
}
```

## Test Utilities

### Database Helpers

```typescript
// tests/e2e/helpers/database.ts
export class DatabaseHelper {
  static async cleanupAttributes() {
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
  }

  static async createTestAttribute(data: Partial<Attribute>) {
    return await prisma.attribute.create({
      data: {
        name: "Test Attribute",
        slug: "test-attribute",
        type: "TEXT",
        isRequired: false,
        isFilterable: true,
        sortOrder: 0,
        ...data
      }
    });
  }

  static async createTestAttributeWithValues(
    attributeData: Partial<Attribute>,
    values: string[]
  ) {
    const attribute = await this.createTestAttribute(attributeData);
    
    await prisma.attributeValue.createMany({
      data: values.map((value, index) => ({
        attributeId: attribute.id,
        value,
        slug: generateSlug(value),
        sortOrder: index + 1
      }))
    });

    return attribute;
  }
}
```

### Authentication Helpers

```typescript
// tests/e2e/helpers/auth.ts
export class AuthHelper {
  static async loginAsAdmin(page: Page) {
    await page.goto('/admin/login');
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'password');
    await page.click('[data-testid="login-btn"]');
    await page.waitForURL('/admin/dashboard');
  }

  static async logout(page: Page) {
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="logout-btn"]');
  }
}
```

## Visual Testing

### Screenshot Comparison

```typescript
test('should match visual design', async ({ page }) => {
  await page.goto('/admin/attributes');
  
  // Take screenshot and compare
  await expect(page).toHaveScreenshot('attributes-list.png');
});

test('should match create form design', async ({ page }) => {
  await page.goto('/admin/attributes/create');
  
  await expect(page.locator('[data-testid="create-form"]')).toHaveScreenshot('create-form.png');
});
```

### Responsive Testing

```typescript
test.describe('Mobile Responsive', () => {
  test.use({ viewport: { width: 375, height: 667 } });

  test('should display mobile layout', async ({ page }) => {
    await page.goto('/admin/attributes');
    
    // Check mobile-specific elements
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();
    await expect(page.locator('[data-testid="desktop-sidebar"]')).not.toBeVisible();
  });
});
```

## Performance Testing

### Load Time Tests

```typescript
test('should load attributes list quickly', async ({ page }) => {
  const startTime = Date.now();
  
  await page.goto('/admin/attributes');
  await page.waitForSelector('[data-testid="attribute-row"]');
  
  const loadTime = Date.now() - startTime;
  expect(loadTime).toBeLessThan(2000); // 2 seconds
});
```

## Best Practices

### 1. Test Data Management

```typescript
test.beforeEach(async () => {
  // Clean state for each test
  await DatabaseHelper.cleanupAttributes();
});

test.afterAll(async () => {
  // Final cleanup
  await DatabaseHelper.cleanupAttributes();
});
```

### 2. Reliable Selectors

```typescript
// Good: Use data-testid
await page.click('[data-testid="submit-btn"]');

// Avoid: Fragile selectors
await page.click('.btn.btn-primary'); // Can break with CSS changes
await page.click('button:nth-child(2)'); // Position-dependent
```

### 3. Wait Strategies

```typescript
// Wait for element to be visible
await page.waitForSelector('[data-testid="success-message"]');

// Wait for network to be idle
await page.waitForLoadState('networkidle');

// Wait for specific condition
await page.waitForFunction(() => 
  document.querySelectorAll('[data-testid="attribute-row"]').length > 0
);
```

### 4. Error Handling

```typescript
test('should handle server errors gracefully', async ({ page }) => {
  // Mock server error
  await page.route('/api/admin/attributes', route => 
    route.fulfill({ status: 500, body: 'Server Error' })
  );

  await page.goto('/admin/attributes');
  
  await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
  await expect(page.locator('[data-testid="retry-btn"]')).toBeVisible();
});
```
