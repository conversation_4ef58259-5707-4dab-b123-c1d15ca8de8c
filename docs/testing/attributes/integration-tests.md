# Hướng dẫn Integration Testing cho Attributes

## Tổng quan

Integration tests kiểm tra tương tác giữa các components, API endpoints, và database. Chúng đảm bảo các phần khác nhau của hệ thống hoạt động đúng khi kết hợp với nhau.

## Cài đặt và Cấu hình

### Database Test Environment

```bash
# Tạo test database
createdb ns_shop_test

# Cấu hình environment variables
echo "DATABASE_URL=postgresql://user:password@localhost:5432/ns_shop_test" > .env.test
echo "NODE_ENV=test" >> .env.test
```

### Test Setup

**File**: `tests/setup.ts`

```typescript
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL_TEST
    }
  }
});

beforeAll(async () => {
  // Migrate test database
  await prisma.$executeRaw`CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`;
});

afterAll(async () => {
  await prisma.$disconnect();
});

export { prisma };
```

## Cấu trúc Integration Tests

### 1. API Endpoints Integration

**File**: `tests/integration/api/admin/attributes.test.ts`

```typescript
import { NextRequest } from "next/server";
import { prisma } from "@/lib/prisma";
import { GET, POST } from "@/app/api/admin/attributes/route";

describe("Attributes API Integration Tests", () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
  });

  describe("CRUD Operations", () => {
    it("should create, read, update, and delete an attribute", async () => {
      // CREATE
      const createData = {
        name: "Màu sắc",
        type: "COLOR",
        values: [
          { value: "Đỏ", slug: "do", sortOrder: 1 },
          { value: "Xanh", slug: "xanh", sortOrder: 2 }
        ]
      };

      const createRequest = new NextRequest("http://localhost/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(createData)
      });

      const createResponse = await POST(createRequest);
      expect(createResponse.status).toBe(201);

      const createdAttribute = await createResponse.json();
      const attributeId = createdAttribute.id;

      // READ
      const getRequest = new NextRequest("http://localhost/api/admin/attributes");
      const getResponse = await GET(getRequest);
      
      const listData = await getResponse.json();
      expect(listData.data).toHaveLength(1);
      expect(listData.data[0].id).toBe(attributeId);

      // Verify in database
      const dbAttribute = await prisma.attribute.findUnique({
        where: { id: attributeId },
        include: { values: true }
      });

      expect(dbAttribute).toBeTruthy();
      expect(dbAttribute?.values).toHaveLength(2);
    });
  });

  describe("Filtering and Pagination", () => {
    beforeEach(async () => {
      // Create test data
      await prisma.attribute.createMany({
        data: [
          {
            name: "Màu sắc",
            slug: "mau-sac",
            type: "COLOR",
            isRequired: false,
            isFilterable: true,
            sortOrder: 1,
          },
          {
            name: "Kích thước",
            slug: "kich-thuoc",
            type: "SIZE",
            isRequired: true,
            isFilterable: true,
            sortOrder: 2,
          }
        ]
      });
    });

    it("should filter by search term", async () => {
      const request = new NextRequest("http://localhost/api/admin/attributes?search=màu");
      const response = await GET(request);
      
      const data = await response.json();
      expect(data.data).toHaveLength(1);
      expect(data.data[0].name).toBe("Màu sắc");
    });

    it("should filter by type", async () => {
      const request = new NextRequest("http://localhost/api/admin/attributes?type=COLOR");
      const response = await GET(request);
      
      const data = await response.json();
      expect(data.data).toHaveLength(1);
      expect(data.data[0].type).toBe("COLOR");
    });

    it("should handle pagination", async () => {
      const request = new NextRequest("http://localhost/api/admin/attributes?page=1&limit=1");
      const response = await GET(request);
      
      const data = await response.json();
      expect(data.data).toHaveLength(1);
      expect(data.pagination.total).toBe(2);
      expect(data.pagination.pages).toBe(2);
    });
  });
});
```

### 2. Attribute Values Integration

**File**: `tests/integration/api/admin/attribute-values.test.ts`

```typescript
describe("Attribute Values API Integration Tests", () => {
  let testAttributeId: string;

  beforeEach(async () => {
    // Create test attribute
    const testAttribute = await prisma.attribute.create({
      data: {
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR",
        isRequired: false,
        isFilterable: true,
        sortOrder: 1,
      }
    });

    testAttributeId = testAttribute.id;
  });

  it("should manage attribute values lifecycle", async () => {
    // CREATE VALUE
    const createData = {
      value: "Đỏ",
      slug: "do",
      sortOrder: 1,
    };

    const createResponse = await POST(
      new NextRequest(`http://localhost/api/admin/attributes/${testAttributeId}/values`, {
        method: "POST",
        body: JSON.stringify(createData)
      }),
      { params: { id: testAttributeId } }
    );

    expect(createResponse.status).toBe(201);
    const createdValue = await createResponse.json();

    // VERIFY IN DATABASE
    const dbValue = await prisma.attributeValue.findUnique({
      where: { id: createdValue.id }
    });

    expect(dbValue?.value).toBe("Đỏ");
    expect(dbValue?.attributeId).toBe(testAttributeId);

    // UPDATE VALUE
    const updateData = { value: "Đỏ đậm", slug: "do-dam" };
    const updateResponse = await PUT(
      new NextRequest(`http://localhost/api/admin/attributes/${testAttributeId}/values/${createdValue.id}`, {
        method: "PUT",
        body: JSON.stringify(updateData)
      }),
      { params: { id: testAttributeId, valueId: createdValue.id } }
    );

    expect(updateResponse.status).toBe(200);

    // VERIFY UPDATE
    const updatedValue = await prisma.attributeValue.findUnique({
      where: { id: createdValue.id }
    });

    expect(updatedValue?.value).toBe("Đỏ đậm");
    expect(updatedValue?.slug).toBe("do-dam");

    // DELETE VALUE
    const deleteResponse = await DELETE(
      new NextRequest(`http://localhost/api/admin/attributes/${testAttributeId}/values/${createdValue.id}`, {
        method: "DELETE"
      }),
      { params: { id: testAttributeId, valueId: createdValue.id } }
    );

    expect(deleteResponse.status).toBe(200);

    // VERIFY DELETION
    const deletedValue = await prisma.attributeValue.findUnique({
      where: { id: createdValue.id }
    });

    expect(deletedValue).toBeNull();
  });

  it("should handle value reordering", async () => {
    // Create multiple values
    const values = await Promise.all([
      prisma.attributeValue.create({
        data: { attributeId: testAttributeId, value: "Đỏ", slug: "do", sortOrder: 1 }
      }),
      prisma.attributeValue.create({
        data: { attributeId: testAttributeId, value: "Xanh", slug: "xanh", sortOrder: 2 }
      }),
      prisma.attributeValue.create({
        data: { attributeId: testAttributeId, value: "Vàng", slug: "vang", sortOrder: 3 }
      })
    ]);

    // Reorder values
    const reorderData = {
      values: [
        { id: values[2].id, sortOrder: 1 }, // Vàng -> 1
        { id: values[0].id, sortOrder: 2 }, // Đỏ -> 2
        { id: values[1].id, sortOrder: 3 }, // Xanh -> 3
      ]
    };

    const reorderResponse = await PUT(
      new NextRequest(`http://localhost/api/admin/attributes/${testAttributeId}/values/reorder`, {
        method: "PUT",
        body: JSON.stringify(reorderData)
      }),
      { params: { id: testAttributeId } }
    );

    expect(reorderResponse.status).toBe(200);

    // Verify new order in database
    const reorderedValues = await prisma.attributeValue.findMany({
      where: { attributeId: testAttributeId },
      orderBy: { sortOrder: "asc" }
    });

    expect(reorderedValues[0].value).toBe("Vàng");
    expect(reorderedValues[1].value).toBe("Đỏ");
    expect(reorderedValues[2].value).toBe("Xanh");
  });
});
```

## Database Transaction Tests

### 1. Rollback on Error

```typescript
describe("Database Transactions", () => {
  it("should rollback transaction on error", async () => {
    const createData = {
      name: "Test Attribute",
      type: "SELECT",
      values: [
        { value: "Value 1", slug: "value-1", sortOrder: 1 },
        { value: "Value 2", slug: "value-2", sortOrder: 2 }
      ]
    };

    // Mock database error during value creation
    const originalCreateMany = prisma.attributeValue.createMany;
    prisma.attributeValue.createMany = jest.fn().mockRejectedValue(
      new Error("Database error")
    );

    const response = await POST(
      new NextRequest("http://localhost/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(createData)
      })
    );

    expect(response.status).toBe(500);

    // Verify no attribute was created
    const attributes = await prisma.attribute.findMany();
    expect(attributes).toHaveLength(0);

    // Restore original function
    prisma.attributeValue.createMany = originalCreateMany;
  });
});
```

### 2. Cascade Operations

```typescript
describe("Cascade Operations", () => {
  it("should cascade delete values when attribute is deleted", async () => {
    // Create attribute with values
    const attribute = await prisma.attribute.create({
      data: {
        name: "Test Attribute",
        slug: "test-attribute",
        type: "SELECT"
      }
    });

    await prisma.attributeValue.createMany({
      data: [
        { attributeId: attribute.id, value: "Value 1", slug: "value-1", sortOrder: 1 },
        { attributeId: attribute.id, value: "Value 2", slug: "value-2", sortOrder: 2 }
      ]
    });

    // Delete attribute
    const deleteResponse = await DELETE(
      new NextRequest(`http://localhost/api/admin/attributes/${attribute.id}`, {
        method: "DELETE"
      }),
      { params: { id: attribute.id } }
    );

    expect(deleteResponse.status).toBe(200);

    // Verify cascade deletion
    const remainingValues = await prisma.attributeValue.findMany({
      where: { attributeId: attribute.id }
    });

    expect(remainingValues).toHaveLength(0);
  });
});
```

## Performance Tests

### 1. Response Time Tests

```typescript
describe("Performance Tests", () => {
  it("should respond within acceptable time limits", async () => {
    const startTime = Date.now();
    
    const response = await GET(
      new NextRequest("http://localhost/api/admin/attributes")
    );
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    expect(response.status).toBe(200);
    expect(responseTime).toBeLessThan(200); // 200ms limit
  });

  it("should handle large datasets efficiently", async () => {
    // Create large dataset
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      name: `Attribute ${i}`,
      slug: `attribute-${i}`,
      type: "TEXT",
      sortOrder: i
    }));

    await prisma.attribute.createMany({ data: largeDataset });

    const startTime = Date.now();
    
    const response = await GET(
      new NextRequest("http://localhost/api/admin/attributes?limit=50")
    );
    
    const endTime = Date.now();
    const responseTime = endTime - startTime;

    expect(response.status).toBe(200);
    expect(responseTime).toBeLessThan(500); // 500ms limit for large datasets
  });
});
```

## Chạy Integration Tests

### Commands

```bash
# Chạy tất cả integration tests
npm run test:integration

# Chạy với database reset
npm run test:integration:clean

# Chạy tests cụ thể
npm test tests/integration/api/admin/attributes.test.ts

# Chạy với verbose output
npm run test:integration -- --verbose

# Chạy với coverage
npm run test:integration:coverage
```

### Scripts trong package.json

```json
{
  "scripts": {
    "test:integration": "NODE_ENV=test jest tests/integration",
    "test:integration:clean": "npm run db:reset:test && npm run test:integration",
    "test:integration:coverage": "NODE_ENV=test jest tests/integration --coverage",
    "db:reset:test": "NODE_ENV=test npx prisma migrate reset --force"
  }
}
```

## Test Data Management

### 1. Factories

```typescript
// tests/factories/attribute.factory.ts
export const createAttributeFactory = (overrides = {}) => ({
  name: "Test Attribute",
  slug: "test-attribute",
  type: "TEXT",
  description: "Test description",
  isRequired: false,
  isFilterable: true,
  sortOrder: 0,
  ...overrides
});

export const createAttributeValueFactory = (attributeId: string, overrides = {}) => ({
  attributeId,
  value: "Test Value",
  slug: "test-value",
  sortOrder: 1,
  ...overrides
});
```

### 2. Seeders

```typescript
// tests/seeders/attribute.seeder.ts
export async function seedAttributes() {
  const attributes = await prisma.attribute.createMany({
    data: [
      {
        name: "Màu sắc",
        slug: "mau-sac",
        type: "COLOR",
        isRequired: false,
        isFilterable: true,
        sortOrder: 1
      },
      {
        name: "Kích thước",
        slug: "kich-thuoc",
        type: "SIZE",
        isRequired: true,
        isFilterable: true,
        sortOrder: 2
      }
    ]
  });

  return attributes;
}
```

## Error Handling Tests

### 1. Validation Errors

```typescript
describe("Validation Error Handling", () => {
  it("should return 400 for invalid data", async () => {
    const invalidData = {
      // Missing required name field
      type: "TEXT"
    };

    const response = await POST(
      new NextRequest("http://localhost/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(invalidData)
      })
    );

    expect(response.status).toBe(400);
    const errorData = await response.json();
    expect(errorData.error).toBe("Dữ liệu không hợp lệ");
  });
});
```

### 2. Database Constraint Errors

```typescript
describe("Database Constraint Handling", () => {
  it("should handle unique constraint violations", async () => {
    // Create first attribute
    await prisma.attribute.create({
      data: {
        name: "First Attribute",
        slug: "test-slug",
        type: "TEXT"
      }
    });

    // Try to create second with same slug
    const duplicateData = {
      name: "Second Attribute",
      slug: "test-slug",
      type: "TEXT"
    };

    const response = await POST(
      new NextRequest("http://localhost/api/admin/attributes", {
        method: "POST",
        body: JSON.stringify(duplicateData)
      })
    );

    expect(response.status).toBe(400);
    const errorData = await response.json();
    expect(errorData.error).toBe("Slug đã tồn tại");
  });
});
```

## Best Practices

### 1. Test Isolation

```typescript
describe("Test Suite", () => {
  beforeEach(async () => {
    // Clean slate for each test
    await prisma.productAttribute.deleteMany();
    await prisma.attributeValue.deleteMany();
    await prisma.attribute.deleteMany();
  });

  afterEach(async () => {
    // Additional cleanup if needed
    await cleanupTestData();
  });
});
```

### 2. Realistic Test Data

```typescript
const vietnameseTestData = {
  attributes: [
    { name: "Màu sắc", values: ["Đỏ", "Xanh dương", "Vàng"] },
    { name: "Kích thước", values: ["S", "M", "L", "XL"] },
    { name: "Chất liệu", values: ["Cotton", "Polyester", "Lụa"] }
  ]
};
```

### 3. Comprehensive Assertions

```typescript
it("should create attribute with complete verification", async () => {
  const response = await POST(createRequest);
  
  // API response verification
  expect(response.status).toBe(201);
  const responseData = await response.json();
  expect(responseData.name).toBe(testData.name);
  
  // Database verification
  const dbAttribute = await prisma.attribute.findUnique({
    where: { id: responseData.id },
    include: { values: true }
  });
  
  expect(dbAttribute).toBeTruthy();
  expect(dbAttribute?.values).toHaveLength(testData.values.length);
  
  // Business logic verification
  expect(dbAttribute?.slug).toBe(generateSlug(testData.name));
});
```
